import { notFound } from 'next/navigation'

import React from "react";
import { posts } from '#velite'
import { CodeHighlighter } from "@/components/code-highlighter";
import { MDXContent } from "@/components/mdx-components";
import Link from "next/link";
import { Button } from "@/components/ui/button";

interface PostProps {
  params: Promise<{ slug: string }>
}

function getPostBySlug(slug: string) {
  return posts.find(post => post.slug === slug)
}

export default async function PostPage({ params }: PostProps) {
  const { slug } = await params
  const post = getPostBySlug(slug)

  if (post == null) {
    notFound()
  }
  else {
    return (
      <article className="flex flex-col 2xl:max-w-[900px] max-w-[700px] ">
        <div className="flex flex-col gap-2 items-center mb-8">
          <h1 className="text-4xl font-bold text-center">{post.title}</h1>
          <p className="">{post.description}</p>
          <p><b>{post.metadata.readingTime}</b> minute read</p>
        </div>

        <CodeHighlighter />
        <MDXContent code={post.body} />

        <footer className="flex flex-row">
          <Button variant="link" asChild className="px-0">
            <Link href={post.lastUpdatedGitCommit}>
              Last updated: {
                new Date(post.lastModified).toLocaleDateString(undefined, {
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                  hour: "numeric",
                  minute: "numeric",
                })
              }
            </Link>
          </Button>
        </footer>
      </article>
    )
  }
}