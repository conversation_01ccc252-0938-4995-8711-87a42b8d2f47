import {createFeed} from '@/lib/feed'
import {NextResponse} from 'next/server'

export async function GET() {
  const feed = createFeed()
  let rss = feed.rss2()

  // Add xmlns:atom namespace
  rss = rss.replace(
    /<rss([^>]*)>/,
    `<rss$1 xmlns:atom="http://www.w3.org/2005/Atom">`
  )

  // Add <atom:link> inside <channel>
  rss = rss.replace(
    '<channel>',
    `<channel>
  <atom:link href="https://schikirianskilukas.de/posts/rss.xml" rel="self" type="application/rss+xml"/>`
  )

  return new NextResponse(rss, {
    headers: {
      'Content-Type': 'application/xml; charset=utf-8',
    },
  })
}
