import { Feed } from 'feed'
import { posts } from '#velite'

export function createFeed() {
  const feed = new Feed({
    title: '<PERSON><PERSON>',
    description: 'Personal website of <PERSON><PERSON>',
    id: 'https://schikirianskilukas.de',
    link: 'https://schikirianskilukas.de',
    language: 'en',
    image: 'https://schikirianskilukas.de/og-image.png',
    favicon: 'https://schikirianskilukas.de/favicon.ico',
    copyright: `All rights reserved ${new Date().getFullYear()}, <PERSON><PERSON>,
    updated: new Date(),
    generator: 'Next.js using Feed for Node.js',
    feedLinks: {
      rss2: 'https://schikirianskilukas.de/posts/rss.xml',
      atom: 'https://schikirianskilukas.de/posts/atom.xml',
      json: 'https://schikirianskilukas.de/posts/feed.json',
    },
    author: {
      name: '<PERSON><PERSON>',
      email: 'luka<PERSON>@schikirianski.de',
      link: 'https://schikirianskilukas.de',
    },
  })

  const publishedPosts = posts
    .filter(post => !post.draft)
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())

  publishedPosts.forEach(post => {
    const postUrl = `https://schikirianskilukas.de/blog/${post.slug}`

    feed.addItem({
      title: post.title,
      id: postUrl,
      link: postUrl,
      description: post.description || '',
      content: post.excerpt || '',
      author: [
        {
          name: 'Lukas Schikirianski',
          email: '<EMAIL>',
          link: 'https://schikirianskilukas.de',
        },
      ],
      date: new Date(post.date),
    })
  })

  return feed
}