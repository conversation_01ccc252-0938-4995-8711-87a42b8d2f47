import {posts} from '#velite'
import type {BlogPost} from './types'

export const blog = {
  // Get all posts
  index: () => posts as unknown as BlogPost[],

  // Find a post by slug
  // @ts-expect-error TS2769
  find: (slug: string) => posts.find((post: BlogPost) => post.slug === slug) as BlogPost | undefined,

  // Get posts sorted by date (newest first)
  // @ts-expect-error 2345
  getSorted: () => posts.sort((a: BlogPost, b: BlogPost) => new Date(b.date).getTime() - new Date(a.date).getTime()
  ) as unknown as BlogPost[]
}
