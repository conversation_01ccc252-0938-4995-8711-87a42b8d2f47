{"name": "schikirianskilukas.de", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --experimental-https", "dev:velite": "velite dev", "build": "next lint && velite && next build", "start": "next start", "lint": "next lint", "velite": "velite"}, "dependencies": {"@fontsource/jetbrains-mono": "^5.2.6", "@next/mdx": "^15.4.1", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-slot": "^1.2.3", "@rehype-pretty/transformers": "^0.13.2", "@shikijs/rehype": "^3.9.2", "@shikijs/transformers": "^3.9.2", "@tabler/icons-react": "^3.34.0", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "feed": "^5.1.0", "lucide-react": "^0.525.0", "next": "15.4.1", "next-themes": "^0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "rehype-pretty-code": "^0.14.1", "remark-code-meta": "^0.4.1", "shiki": "^3.9.2", "tailwind-merge": "^3.3.1", "velite": "^0.2.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.1", "eslint-plugin-mdx": "^3.6.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}}