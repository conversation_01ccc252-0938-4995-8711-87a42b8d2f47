"use client"

import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator
} from "@/components/ui/command"
import * as React from "react";
import {useTheme} from "next-themes"
import {Moon, Sun} from "lucide-react"

export function CommandMenu() {
  const [open, setOpen] = React.useState(false)

  React.useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === "k" && (e.metaKey || e.ctrlKey)) {
        e.preventDefault()
        setOpen((open) => !open)
      }
    }
    document.addEventListener("keydown", down)
    return () => document.removeEventListener("keydown", down)
  }, [])

  const { theme, resolvedTheme, setTheme } = useTheme()

  const toggleTheme = () => {
    switch (theme) {
      case "light":
        setTheme("dark");
        break;
      case "dark":
        setTheme("light");
        break;
      case "system":
        setTheme(resolvedTheme === "light" ? "dark" : "light");
        break;
    }
  }

  return (
    <CommandDialog open={open} onOpenChange={setOpen}>
      <CommandInput placeholder="Type a command or search..." />
      <CommandList>
        <CommandEmpty>No results found.</CommandEmpty>
        <CommandGroup heading="Suggestions">
          <CommandItem onSelect={() => {
            toggleTheme();
            setOpen(false);
          }}>
            { theme === "dark" ?
              <Sun className="h-[1.2rem] w-[1.2rem]" /> :
              <Moon className="h-[1.2rem] w-[1.2rem]" />
            }
            Toggle Theme
          </CommandItem>
        </CommandGroup>
        <CommandSeparator />
        <CommandGroup heading="Commands">
          <CommandItem onSelect={() => {
            setTheme("light");
            setOpen(false);
          }}>
            <Sun className="h-[1.2rem] w-[1.2rem]" />
            Switch to Light Theme
          </CommandItem>
          <CommandItem onSelect={() => {
            setTheme("dark");
            setOpen(false);
          }}>
            <Moon className="h-[1.2rem] w-[1.2rem]" />
            Switch to Dark Theme
          </CommandItem>
        </CommandGroup>
      </CommandList>
    </CommandDialog>
  )
}