import React from "react";

interface CopyButtonProps {
  /** The text content to copy to clipboard */
  textToCopy: string;
}

const CopyIcon = () => (
  <svg
    width="12"
    height="12"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
    <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
  </svg>
)

export const CopyButton: React.FC<CopyButtonProps> = ({ textToCopy }) => {
  const [status, setStatus] = React.useState<'idle' | 'success' | 'error'>('idle')

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(textToCopy)
      setStatus('success')

      setTimeout(() => {
        setStatus('idle')
      }, 2000)
    } catch (err) {
      console.warn('Failed to copy text:', err)
      setStatus('error')

      setTimeout(() => {
        setStatus('idle')
      }, 2000)
    }
  }

  const getButtonText = () => {
    switch (status) {
      case 'success':
        return 'Copied!'
      case 'error':
        return 'Failed'
      default:
        return 'Copy'
    }
  }

  const getAriaLabel = () => {
    switch (status) {
      case 'success':
        return 'Code copied to clipboard'
      case 'error':
        return 'Failed to copy code'
      default:
        return 'Copy code to clipboard'
    }
  }

  return (
    <button
      type="button"
      aria-label={getAriaLabel()}
      title={getAriaLabel()}
      className="font-mono absolute top-2 right-2 px-3 py-1.5 text-xs bg-gray-700 hover:bg-gray-600 text-white rounded-md opacity-0 hover:opacity-100 transition-all duration-200 focus:opacity-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-800"
      onClick={handleCopy}
    >
      <span className="flex items-center gap-1">
        <CopyIcon />
        <span>{getButtonText()}</span>
      </span>
    </button>
  )
}