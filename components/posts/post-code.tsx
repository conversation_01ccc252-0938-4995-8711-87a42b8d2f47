import React from "react";
import { cn } from "@/lib/utils";

interface PostPreProps extends React.HTMLAttributes<HTMLPreElement> {
  children: React.ReactNode;
  "data-theme"?: string;
  "data-line-numbers"?: boolean;
}

export const PostPre = ({ children, className, ...props }: PostPreProps) => (
  <pre 
    className={cn(
      "overflow-x-auto rounded-lg border bg-muted p-4 text-sm mb-6",
      "font-mono",
      className
    )} 
    {...props}
  >
    {children}
  </pre>
);

interface PostCodeProps extends React.HTMLAttributes<HTMLElement> {
  children: React.ReactNode;
  className?: string;
}

export const PostCode = ({ children, className, ...props }: PostCodeProps) => {
  // Check if this is inline code (not inside a pre element)
  const isInline = !className?.startsWith('language-');
  
  if (isInline) {
    return (
      <code 
        className={cn(
          "relative rounded bg-muted px-[0.3rem] py-[0.2rem] text-sm font-mono",
          className
        )} 
        {...props}
      >
        {children}
      </code>
    );
  }

  // This is a code block inside a pre element
  return (
    <code 
      className={cn(
        "bg-transparent p-0 text-sm font-mono",
        className
      )} 
      {...props}
    >
      {children}
    </code>
  );
};
