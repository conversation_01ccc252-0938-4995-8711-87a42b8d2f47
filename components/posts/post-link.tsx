import React from "react";
import Link from "next/link";

export const variants = {
  default: "text-sm font-medium text-primary underline-offset-4 hover:underline",
  outline: "text-sm font-medium text-primary p-1 px-2 border-2 border-primary rounded-sm dark:hover:bg-primary hover:bg-primary hover:text-white dark:hover:text-black transition duration-200 ease-in-out",
}

interface PostLinkProps extends React.AnchorHTMLAttributes<HTMLAnchorElement> {
  href?: string;
  variant?: keyof typeof variants;
  children: React.ReactNode;
}

export const PostLink = ({ href, children, variant = "default", ...props }: PostLinkProps) => {
  return (
    <Link href={href || "#"} className={variants[variant]} {...props}>
      {children}
    </Link>
  );
}