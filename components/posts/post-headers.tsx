import React from "react";
import {PostLink} from "@/components/posts/post-link";

const linkStyling = "text-muted-foreground hover:text-primary transition duration-200 ease-in-out"

function getLink(children: React.ReactNode) {
  return "#" + children!.toString().toLowerCase().replace(/\s+/g, "-")
}


export const PostH1 = ({ children, ...props }: React.AnchorHTMLAttributes<HTMLAnchorElement>) => (
  // @ts-expect-error TS2322
  <h1 className="text-2xl font-bold mb-1" {...props}>
    <PostLink href={getLink(children)} className={linkStyling} id={getLink(children)}>#</PostLink>
    <> </>
    {children}
  </h1>
)

export const PostH2 = ({ children, ...props }: React.AnchorHTMLAttributes<HTMLAnchorElement>) => (
  // @ts-expect-error TS2322
  <h2 className="text-xl font-bold mb-1" {...props}>
    <PostLink href={getLink(children)} className={linkStyling} id={getLink(children)}>##</PostLink>
    <> </>
    {children}
  </h2>
)

export const PostH3 = ({ children, ...props }: React.AnchorHTMLAttributes<HTMLAnchorElement>) => (
  // @ts-expect-error TS2322
  <h3 className="text-lg font-bold mb-1" {...props}>
    <PostLink href={getLink(children)} className={linkStyling} id={getLink(children)}>###</PostLink>
    <> </>
    {children}
  </h3>
)
