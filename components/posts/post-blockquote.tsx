import React from "react";
import { cn } from "@/lib/utils";
import {
  Info,
  CircleQuestionMark,
  CircleCheck,
  TriangleAlert,
  OctagonAlert
} from "lucide-react";

const styles = {
  info: "bg-gray-100 text-gray-900",
  question: " bg-blue-100 text-blue-900",
  success: "bg-green-100 text-green-900",
  warning: "bg-yellow-100 text-yellow-900",
  error: "bg-red-100 text-red-900",
};

const icons = {
  info: Info,
  question: CircleQuestionMark,
  success: CircleCheck,
  warning: <PERSON><PERSON>lert,
  error: OctagonAlert,
};

export const PostBlockquote = ({
  children,
  className,
  type = "info",
  ...props
}: {
  children: React.ReactNode;
  className?: string;
  type?: "info" | "question" | "success" | "warning" | "error";
} & React.HTMLAttributes<HTMLDivElement>) => {
  const IconComponent = icons[type];

  return (
    <div className={cn("w-full mb-6 rounded-lg border-2 p-4 border-primary", styles[type], className)} {...props}>
      <div className="flex">
        <IconComponent className="w-5 h-5 mt-0.5 mr-2 flex-shrink-0" />
        {/* [&_*]:mb-0 - Set bottom margin of all children to 0 */}
        <div className="[&_*]:mb-0">
          {children}
        </div>
      </div>
    </div>
  );
};
