/**
 * Modular Code Highlighter with Plugin System
 *
 * This component provides syntax highlighting for code blocks with a plugin-based
 * architecture that makes it easy to enable/disable features.
 *
 * USAGE:
 * 1. Enable/disable plugins by commenting/uncommenting them in the defaultConfig
 * 2. Add custom plugins by implementing the CodeHighlighterPlugin interface
 * 3. Configure themes, classes, and timing in the defaultConfig
 *
 * AVAILABLE PLUGINS:
 * - lineNumbersPlugin: Adds line numbers when 'showLineNumbers' is detected
 * - copyButtonPlugin: Adds a copy button to all code blocks (with proper HTML semantics)
 * - languageLabelPlugin: Shows the language name in the top-left corner
 *
 * MARKDOWN EXAMPLES:
 * ```python showLineNumbers
 * def hello():
 *     print("Hello, World!")
 * ```
 *
 * FEATURES:
 * ✅ Syntax highlighting with Shiki
 * ✅ Line numbers support
 * ✅ Copy button with proper HTML semantics and accessibility
 * ✅ Plugin-based architecture for easy customization
 * ✅ Error handling and graceful degradation
 * ✅ Full keyboard accessibility
 * ✅ Theme support (automatically switches between one-dark-pro and one-light-pro)
 */

"use client"

import { useEffect } from 'react'
import { createRoot } from 'react-dom/client'
import { codeToHtml } from 'shiki'
import { useTheme } from 'next-themes'
import { CopyButton } from '@/components/ui/copy-button'


interface CodeBlockInfo {
  codeElement: HTMLElement
  preElement: HTMLPreElement
  language: string
  code: string
  pageSource: string
}

interface CodeHighlighterPlugin {
  name: string
  detect: (info: CodeBlockInfo) => boolean
  process: (info: CodeBlockInfo, newPre: HTMLPreElement) => void
}

interface CodeHighlighterConfig {
  theme: string
  delay: number
  plugins: CodeHighlighterPlugin[]
}


/**
 * Adds a copy button to code blocks using React component
 */
const copyButtonPlugin: CodeHighlighterPlugin = {
  name: 'copy-button',
  detect: () => true, // Always add copy button
  process: (info, newPre) => {
    // Create a container div for the React component
    const buttonContainer = document.createElement('div')

    // Make the pre element relative positioned
    newPre.style.position = 'relative'
    newPre.appendChild(buttonContainer)

    // Create React root and render the CopyButton component
    const root = createRoot(buttonContainer)
    root.render(
      <CopyButton textToCopy={info.code} />
    )

    // Show button on hover and focus
    const showButton = () => {
      const button = buttonContainer.querySelector('button')
      if (button) {
        button.style.opacity = '0.9'
      }
    }
    const hideButton = () => {
      const button = buttonContainer.querySelector('button')
      if (button && document.activeElement !== button) {
        button.style.opacity = '0'
      }
    }

    newPre.addEventListener('mouseenter', showButton)
    newPre.addEventListener('mouseleave', hideButton)

    // Add focus/blur listeners to the button once it's rendered
    setTimeout(() => {
      const button = buttonContainer.querySelector('button')
      if (button) {
        button.addEventListener('focus', showButton)
        button.addEventListener('blur', hideButton)
      }
    }, 0)
  }
}


const defaultConfig: CodeHighlighterConfig = {
  theme: 'one-dark-pro',
  delay: 0,
  plugins: [
    copyButtonPlugin,
  ]
}


export function CodeHighlighter(config: Partial<CodeHighlighterConfig> = {}) {
  const finalConfig = { ...defaultConfig, ...config }

  useEffect(() => {
    const highlightCode = async () => {
      // Find all code blocks that need highlighting
      const codeBlocks = document.querySelectorAll('pre code[class*="language-"]')
      const pageSource = document.documentElement.outerHTML

      for (const block of codeBlocks) {
        const codeElement = block as HTMLElement
        const preElement = codeElement.parentElement as HTMLPreElement

        // Extract language from className
        const className = codeElement.className
        const languageMatch = className.match(/language-(\w+)/)
        const language = languageMatch ? languageMatch[1] : 'text'

        // Get the code content
        const code = codeElement.textContent || ''

        // Create info object for plugins
        const info: CodeBlockInfo = {
          codeElement,
          preElement,
          language,
          code,
          pageSource
        }

        try {
          // Generate highlighted HTML
          const html = await codeToHtml(code, {
            lang: language,
            theme: finalConfig.theme
          })

          // Replace the pre element with the highlighted version
          const tempDiv = document.createElement('div')
          tempDiv.innerHTML = html
          const newPre = tempDiv.firstElementChild as HTMLPreElement

          if (newPre) {
            // Apply base classes
            newPre.className = `${newPre.className}`

            // Run enabled plugins
            finalConfig.plugins.forEach(plugin => {
              try {
                if (plugin.detect(info)) {
                  plugin.process(info, newPre)
                }
              } catch (error) {
                console.warn(`Plugin "${plugin.name}" failed:`, error)
              }
            })

            // Replace the original element
            preElement.replaceWith(newPre)
          }
        } catch (error) {
          console.warn(`Failed to highlight code block with language "${language}":`, error)
        }
      }
    }

    // Run highlighting after a short delay to ensure DOM is ready
    const timer = setTimeout(highlightCode, finalConfig.delay)

    return () => clearTimeout(timer)
  })

  return null
}
