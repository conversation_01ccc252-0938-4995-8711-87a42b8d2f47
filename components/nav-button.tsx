"use client"

import {<PERSON><PERSON>} from "@/components/ui/button";
import Link from "next/link";
import {usePathname} from "next/navigation";

interface NavButtonProps {
  href: string;
  title: string;
}

export function NavButton({ href, title }: NavButtonProps) {
  const pathname = usePathname();
  
  return (
    <Button
      asChild
      variant="ghost"
      className={pathname === href ? "bg-accent text-accent-foreground dark:bg-accent/50" : ""}
    >
      <Link href={href}>{title}</Link>
    </Button>
  );
}
