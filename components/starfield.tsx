'use client';

import React, { useEffect, useRef } from 'react';
import { useTheme } from 'next-themes';

type Star = {
  x: number;
  y: number;
  vxBase: number;
  vyBase: number;
  vxBurst: number;
  vyBurst: number;
  radius: number;
};

export default function Starfield() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const { theme } = useTheme();

  const starsRef = useRef<Star[]>([]);
  const mouseRef = useRef<{ x: number; y: number; active: boolean; down: boolean }>({
    x: -1,
    y: -1,
    active: false,
    down: false,
  });
  const dimensionsRef = useRef({ width: 0, height: 0 });
  const animationFrameIdRef = useRef<number | null>(null);

  const isTouchActiveRef = useRef(false);

  const initialize = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const width = window.innerWidth;
    const height = window.innerHeight;

    canvas.width = width;
    canvas.height = height;

    dimensionsRef.current = { width, height };
    mouseRef.current = { x: -1, y: -1, active: false, down: false };

    const starCount = Math.floor((width * height) / 15000);
    const baseSpeed = 0.5;
    starsRef.current = [];
    for (let i = 0; i < starCount; i++) {
      starsRef.current.push({
        x: Math.random() * width,
        y: Math.random() * height,
        vxBase: (Math.random() - 0.5) * Math.random() * baseSpeed,
        vyBase: (Math.random() - 0.5) * Math.random() * baseSpeed,
        vxBurst: 0,
        vyBurst: 0,
        // Radius: 1.25 - 2.5
        radius: Math.random() * (2.5 - 1.25) + 1.25
      });
    }
  };

  useEffect(() => {
    initialize();

    const handleResize = () => {
      initialize();
    };
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  useEffect(() => {
    const drag = 0.95;

    const animate = () => {
      const canvas = canvasRef.current;
      if (!canvas) return;
      const ctx = canvas.getContext('2d');
      if (!ctx) return;

      const { width, height } = dimensionsRef.current;
      ctx.clearRect(0, 0, width, height);

      const stars = starsRef.current;
      const mouse = mouseRef.current;

      for (const star of stars) {
        star.x += star.vxBase + star.vxBurst;
        star.y += star.vyBase + star.vyBurst;

        star.vxBurst *= drag;
        star.vyBurst *= drag;

        if (star.x < 0) {
          star.x = 0;
          star.vxBase *= -1;
        } else if (star.x > width) {
          star.x = width;
          star.vxBase *= -1;
        }
        if (star.y < 0) {
          star.y = 0;
          star.vyBase *= -1;
        } else if (star.y > height) {
          star.y = height;
          star.vyBase *= -1;
        }

        ctx.beginPath();
        ctx.arc(star.x, star.y, star.radius, 0, Math.PI * 2);
        ctx.fillStyle = theme === 'dark' ? '#fff' : '#000';
        ctx.fill();

        const showLines = isTouchActiveRef.current ? mouse.down : mouse.active;

        if (showLines && mouse.x >= 0 && mouse.y >= 0) {
          const dx = star.x - mouse.x;
          const dy = star.y - mouse.y;
          const dist = Math.sqrt(dx * dx + dy * dy);
          if (dist < 120) {
            ctx.beginPath();
            ctx.moveTo(star.x, star.y);
            ctx.lineTo(mouse.x, mouse.y);
            const alpha = 1 - dist / 120;
            ctx.strokeStyle =
              theme === 'dark'
                ? `rgba(255,255,255,${alpha})`
                : `rgba(0,0,0,${alpha})`;
            ctx.stroke();
          }
        }
      }

      animationFrameIdRef.current = requestAnimationFrame(animate);
    };

    animationFrameIdRef.current = requestAnimationFrame(animate);

    // Touch handlers (mobile)
    const handleTouchStart = (e: TouchEvent) => {
      if (e.touches.length > 0) {
        isTouchActiveRef.current = true;
        const touch = e.touches[0];
        mouseRef.current.x = touch.clientX;
        mouseRef.current.y = touch.clientY;
        mouseRef.current.active = true;
        mouseRef.current.down = true;
      }
    };

    const handleTouchMove = (e: TouchEvent) => {
      if (e.touches.length > 0) {
        const touch = e.touches[0];
        mouseRef.current.x = touch.clientX;
        mouseRef.current.y = touch.clientY;
      }
    };

    const handleTouchEnd = () => {
      isTouchActiveRef.current = false;
      mouseRef.current.down = false;
      mouseRef.current.active = false;
      mouseRef.current.x = -1;
      mouseRef.current.y = -1;
    };

    const handleTouchCancel = () => {
      isTouchActiveRef.current = false;
      mouseRef.current.down = false;
      mouseRef.current.active = false;
      mouseRef.current.x = -1;
      mouseRef.current.y = -1;
    };

    // Mouse handlers (desktop)
    const handleMouseMove = (e: MouseEvent) => {
      if (isTouchActiveRef.current) return;
      mouseRef.current.x = e.clientX;
      mouseRef.current.y = e.clientY;
      mouseRef.current.active = true;
    };

    const handleMouseLeave = () => {
      if (isTouchActiveRef.current) return;
      mouseRef.current.active = false;
      mouseRef.current.x = -1;
      mouseRef.current.y = -1;
    };

    const handleMouseDown = (e: MouseEvent) => {
      if (isTouchActiveRef.current) return;
      mouseRef.current.down = true;
      mouseRef.current.x = e.clientX;
      mouseRef.current.y = e.clientY;

      // Burst effect on click
      const radius = 100;
      for (const star of starsRef.current) {
        const dx = star.x - mouseRef.current.x;
        const dy = star.y - mouseRef.current.y;
        const dist = Math.sqrt(dx * dx + dy * dy);
        if (dist < radius) {
          const force = (1 - dist / radius) * 5;
          const angle = Math.atan2(dy, dx);
          star.vxBurst += Math.cos(angle) * force;
          star.vyBurst += Math.sin(angle) * force;
        }
      }
    };

    const handleMouseUp = () => {
      if (isTouchActiveRef.current) return;
      mouseRef.current.down = false;
    };

    window.addEventListener('touchstart', handleTouchStart);
    window.addEventListener('touchmove', handleTouchMove);
    window.addEventListener('touchend', handleTouchEnd);
    window.addEventListener('touchcancel', handleTouchCancel);

    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('mouseout', handleMouseLeave);
    window.addEventListener('mousedown', handleMouseDown);
    window.addEventListener('mouseup', handleMouseUp);

    return () => {
      window.removeEventListener('touchstart', handleTouchStart);
      window.removeEventListener('touchmove', handleTouchMove);
      window.removeEventListener('touchend', handleTouchEnd);
      window.removeEventListener('touchcancel', handleTouchCancel);

      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseout', handleMouseLeave);
      window.removeEventListener('mousedown', handleMouseDown);
      window.removeEventListener('mouseup', handleMouseUp);

      if (animationFrameIdRef.current) {
        cancelAnimationFrame(animationFrameIdRef.current);
        animationFrameIdRef.current = null;
      }
    };
  }, [theme]);

  return (
    <canvas
      ref={canvasRef}
      className="fixed inset-0 z-[-1] pointer-events-none"
      aria-hidden="true"
    />
  );
}
