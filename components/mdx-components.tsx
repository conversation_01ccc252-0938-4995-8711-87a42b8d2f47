import * as runtime from 'react/jsx-runtime'
import React from "react";

import { PostBlockquote } from "@/components/posts/post-blockquote";
import { PostLinebreak } from "@/components/posts/post-linebreak";
import { PostLink } from "@/components/posts/post-link";
import { PostP } from "@/components/posts/post-p";
import { PostH1, PostH2, PostH3 } from "@/components/posts/post-headers";
import { PostPre, PostCode } from "@/components/posts/post-code";

export const sharedComponents = {
  // Custom components
  PostBlockquote,
  PostLinebreak,
  PostLink,
  // Replace <a> with PostLink
  a: ({ children, ...props }: React.AnchorHTMLAttributes<HTMLAnchorElement> & { children: React.ReactNode }) => (
    <PostLink {...props} variant="default">{children}</PostLink>
  ),
  p: PostP,
  h1: PostH1,
  h2: PostH2,
  h3: PostH3,
  pre: PostPre,
  code: PostCode,
}

const useMDXComponent = (code: string) => {
  const fn = new Function(code)
  return fn({ ...runtime }).default
}

interface MDXContentProps {
  code: string
  components?: Record<string, React.ComponentType>
}

export const MDXContent = ({ code, components }: MDXContentProps) => {
  const Component = useMDXComponent(code)
  return <Component components={{ ...sharedComponents, ...components }} />
}
