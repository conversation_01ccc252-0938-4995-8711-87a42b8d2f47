---
title: Code Highlighting Test
slug: code-highlighting-test
date: 2024-12-01 12:00:00
description: Testing code highlighting with rehype-pretty-code
categories:
  - Test
---

# Code Highlighting Test

This post tests the code highlighting functionality.

## Python Code

```python
def hello_world():
    """A simple hello world function."""
    name = input("What's your name? ")
    print(f"Hello, {name}!")
    
    # Calculate something
    numbers = [1, 2, 3, 4, 5]
    total = sum(numbers)
    return total

if __name__ == "__main__":
    result = hello_world()
    print(f"Sum: {result}")
```

## JavaScript Code

```javascript
const greetUser = (name) => {
    // Modern JavaScript with arrow functions
    const greeting = `Hello, ${name}!`;
    console.log(greeting);
    
    // Array methods
    const numbers = [1, 2, 3, 4, 5];
    const doubled = numbers.map(n => n * 2);
    
    return {
        greeting,
        doubled
    };
};

// Call the function
const result = greetUser("World");
```

## TypeScript Code

```typescript
interface User {
    id: number;
    name: string;
    email: string;
}

class UserService {
    private users: User[] = [];
    
    addUser(user: User): void {
        this.users.push(user);
    }
    
    findUser(id: number): User | undefined {
        return this.users.find(user => user.id === id);
    }
}

const service = new UserService();
service.addUser({ id: 1, name: "John", email: "<EMAIL>" });
```

## Inline Code

Here's some `inline code` in a paragraph. You can also use `const variable = "value"` inline.
