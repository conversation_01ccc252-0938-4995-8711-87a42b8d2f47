---
title: MDX Stylesheet
slug: mdx-stylesheet
date: 2024-05-08 13:00:00
cover: cover.jpg
categories:
  - Test
---

# Header 1
Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed non risus. Suspendisse
lobortis nulla id turpis. In condimentum. In hac habitasse platea dictumst. Nulla
non lectus sed nisl molestie malesuada. <PERSON>ulla sit amet metus. Nulla sit amet
metus. Nulla sit amet metus. Nulla sit amet metus. Nulla sit amet metus.

## Header 2
Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed non risus. Suspendisse
lobortis nulla id turpis. In condimentum. In hac habitasse platea dictumst. Nulla
non lectus sed nisl molestie malesuada. Nulla sit amet metus. Nulla sit amet
metus. Nulla sit amet metus. Nulla sit amet metus. Nulla sit amet metus.

### Header 3
Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed non risus. Suspendisse
lobortis nulla id turpis. In condimentum. In hac habitasse platea dictumst. Nulla
non lectus sed nisl molestie malesuada. Nulla sit amet metus. Nulla sit amet
metus. Nulla sit amet metus. Nulla sit amet metus. Nulla sit amet metus.


<b>Bold text</b>
<PostLinebreak />

<i>Italic text</i>
<PostLinebreak />

<u>Underlined text</u>
<PostLinebreak />

[Link to plain.txt](plain.txt)


<PostBlockquote type="info">This is an info blockquote</PostBlockquote>

<PostBlockquote type="question">This is a question blockquote</PostBlockquote>

<PostBlockquote type="success">This is a success blockquote</PostBlockquote>

<PostBlockquote type="warning">This is a warning blockquote</PostBlockquote>

<PostBlockquote type="error">This is an error blockquote</PostBlockquote>

```py showLineNumbers
user_input: str = input("Enter your name: ")
user_input: str = str(input("Enter your name: "))
print(f"Hello {user_input}")
```
<PostLinebreak />
